<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>ss -ti 输出字段速查与排查指南（4 主题版）</title>
  <style>
    :root{
      /* 默认：深色（中性暗色） */
      --bg:#0b121d; --panel:#0e1626; --card:#101b2b; --muted:#93a4b6; --fg:#e5edf6;
      --accent:#60a5fa; --accent-2:#22d3ee; --good:#34d399; --warn:#f59e0b; --danger:#f43f5e;
      --border:#1e293b; --table-row:rgba(148,163,184,.07); --table-hover:rgba(96,165,250,.10);
      --glow-1: rgba(122,167,255,.22); --glow-2: rgba(92,200,255,.18);
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{
      margin:0; font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, "Noto Sans", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
      background: radial-gradient(1100px 520px at 12% -8%, var(--glow-1), transparent),
                  radial-gradient(900px 460px at 108% 8%, var(--glow-2), transparent),
                  var(--bg);
      color: var(--fg);
      line-height: 1.6;
    }

    .container{max-width:1120px;margin-left:24px;margin-right:auto;padding:28px 16px 64px}
    header{background:var(--panel);border-bottom:1px solid var(--border)}
    .title{max-width:1120px;margin-left:24px;margin-right:auto;padding:28px 16px}
    h1{margin:0;font-size:28px;letter-spacing:.2px}
    .subtitle{margin-top:6px;color:var(--muted);font-size:14px}
    .badges{display:flex;gap:8px;flex-wrap:wrap;margin-top:14px}
    .badge{padding:4px 10px;border:1px solid var(--border);border-radius:999px;color:var(--accent-2);background:rgba(255,255,255,.04)}

    .card{background:linear-gradient(180deg, rgba(255,255,255,.04), rgba(0,0,0,.18));border:1px solid var(--border);border-radius:14px;padding:16px 16px 12px;margin-top:16px}
    .h2{display:flex;align-items:center;gap:10px}
    .h2 .bar{width:8px;height:18px;background:linear-gradient(180deg, var(--accent), var(--accent-2));border-radius:2px;box-shadow:0 0 0 1px rgba(122,167,255,.25) inset}
    h2{margin:8px 0 6px;font-size:20px}
    h3{margin:10px 0 6px;font-size:16px;color:var(--fg)}
    p{margin:8px 0;color:var(--fg)}

    ul{padding-left:18px;margin:6px 0}
    li{margin:4px 0}
    code{background:rgba(2,6,23,.5);padding:2px 6px;border-radius:6px;border:1px solid var(--border)}
    pre{background:#0c1a2a;border:1px solid var(--border);padding:10px;border-radius:10px;overflow:auto;margin:8px 0}
    pre code{background:transparent;border:none;padding:0}
    .callout{border:1px solid var(--border);border-left:3px solid var(--accent-2);background:rgba(255,255,255,.05);border-radius:10px;padding:10px 12px;color:var(--fg);margin:8px 0}

    .theme-switcher{position:fixed;right:16px;bottom:16px;display:flex;gap:8px;z-index:9999}
    .theme-switcher button{padding:6px 10px;border-radius:999px;border:1px solid var(--border);background:rgba(255,255,255,.04);color:var(--fg);cursor:pointer}
    .theme-switcher button.active{outline:2px solid var(--accent)}
  </style>
  <style>
    /* 主题集：深色（默认）/ 淡蓝 / 柔绿 / 暖灰护眼 */
    :root.theme-dark{--bg:#0b121d;--panel:#0e1626;--card:#101b2b;--muted:#93a4b6;--fg:#e5edf6;--accent:#60a5fa;--accent-2:#22d3ee;--good:#34d399;--warn:#f59e0b;--danger:#f43f5e;--border:#1e293b;--table-row:rgba(148,163,184,.07);--table-hover:rgba(96,165,250,.10);--glow-1:rgba(122,167,255,.22);--glow-2:rgba(92,200,255,.18)}
    :root.theme-blue{--bg:#0a1624;--panel:#0e1b2b;--card:#0f1f33;--muted:#a9bdd6;--fg:#e8f1fb;--accent:#5cc8ff;--accent-2:#7aa7ff;--good:#5fd5b5;--warn:#f6b15c;--danger:#ff6b88;--border:#16324b;--table-row:rgba(173,202,237,.06);--table-hover:rgba(92,200,255,.10);--glow-1:rgba(122,167,255,.22);--glow-2:rgba(92,200,255,.18)}
    :root.theme-teal{--bg:#0b1411;--panel:#0e1b16;--card:#102019;--muted:#b6c8be;--fg:#eaf3ee;--accent:#58d3a4;--accent-2:#74c9b6;--good:#6bd3b0;--warn:#e9b562;--danger:#ff7f8a;--border:#193427;--table-row:rgba(150,210,180,.06);--table-hover:rgba(88,211,164,.10);--glow-1:rgba(116,200,164,.18);--glow-2:rgba(88,211,164,.14)}
    :root.theme-warmgray{--bg:#111315;--panel:#15181b;--card:#1a1f25;--muted:#c2c7cc;--fg:#f0f2f4;--accent:#86a8ff;--accent-2:#8bd3ff;--good:#67d4a3;--warn:#efc070;--danger:#ff8aa0;--border:#262b31;--table-row:rgba(200,205,210,.06);--table-hover:rgba(134,168,255,.10);--glow-1:rgba(134,168,255,.16);--glow-2:rgba(139,211,255,.14)}
  </style>
  <script>
    (function(){
      const key='ss-ti-theme';
      const root=document.documentElement;
      const saved=localStorage.getItem(key);
      const initial=saved||'theme-dark';
      root.classList.add(initial);
      window.addEventListener('DOMContentLoaded',()=>{
        document.querySelectorAll('[data-theme]').forEach(btn=>{
          const t=btn.getAttribute('data-theme');
          if(t===initial) btn.classList.add('active');
          btn.addEventListener('click',()=>{
            root.classList.remove('theme-dark','theme-blue','theme-teal','theme-warmgray');
            root.classList.add(t);
            localStorage.setItem(key,t);
            document.querySelectorAll('[data-theme]').forEach(b=>b.classList.remove('active'));
            btn.classList.add('active');
          });
        });
      });
    })();
  </script>
</head>
<body>
  <header>
    <div class="title">
      <h1>ss -ti 输出字段速查与解读</h1>
      <div class="subtitle">4 种护眼主题 · 可切换 · 记忆选择</div>
      <div class="badges">
        <div class="badge">更新：2025-08-19</div>
        <div class="badge">适用：Linux 服务器</div>
        <div class="badge">工具：iproute2 / ss</div>
      </div>
    </div>
  </header>

  <main class="container">
    <section class="card">
      <div class="h2"><span class="bar"></span><h2>概览</h2></div>
      <p>面向读者：运维/后端开发；适用系统：Linux；示例命令：</p>
      <pre><code>ss -ti '( dport = :12809 )' | more</code></pre>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>一、概要行字段（第一行）</h2></div>
      <ul>
        <li><b>State</b>：TCP 状态（ESTAB、CLOSE-WAIT 等）。CLOSE-WAIT 长存多条通常是应用未及时 close()。</li>
        <li><b>Recv-Q / Send-Q</b>：接收队列未读字节 / 发送队列未确认字节。</li>
        <li><b>Local/Peer</b>：本地/对端 IP:Port。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>二、详细 TCP 参数（第二行常见字段）</h2></div>
      <ul>
        <li><b>拥塞算法</b>：bbr/cubic 等；<b>wscale</b>：窗口扩大因子。</li>
        <li><b>rto</b>：重传超时（ms）；<b>rtt</b>：平均时延/抖动（ms）。</li>
        <li><b>ato</b>：ACK 超时；<b>mss/pmtu</b>：最大段/路径 MTU。</li>
        <li><b>rcvmss/advmss</b>：对端最近估计 MSS / 我方宣告 MSS。</li>
        <li><b>cwnd/ssthresh</b>：拥塞窗口/慢启动阈值（段）。</li>
        <li><b>bytes_*</b>、<b>segs_*</b>、<b>data_segs_*</b>：计数。</li>
        <li><b>send/delivery_rate</b>：发送速率估计 / 有效交付速率。</li>
        <li><b>lastsnd/lastrcv/lastack</b>：距上次活跃的毫秒数；<b>pacing_rate</b>：整形速率。</li>
        <li><b>delivered/busy</b>：BBR 用统计段数与忙碌时间；<b>reordering</b>：乱序阈值（段）。</li>
        <li><b>rcv_space/rcv_ssthresh</b>：接收缓冲与阈值；<b>minrtt</b>：最小 RTT；<b>snd_wnd</b>：对端窗口。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>三、BBR 专属区块（bbr:(...)）</h2></div>
      <ul>
        <li><b>bw</b>：瓶颈带宽（kbps）；<b>mrtt</b>：最小 RTT。</li>
        <li><b>pacing_gain / cwnd_gain</b>：>1 探测带宽，<1 收敛控制。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>四、MPTCP 相关（tcp-ulp-mptcp ...）</h2></div>
      <ul>
        <li>启用了 TCP ULP 的 MPTCP 钩子；<b>token</b>：连接标识。</li>
        <li><b>seq/sfseq/ssnoff/maplen</b>：DSS 映射信息；异常或 0 可能未协商成功。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>五、结合样例的快速解读要点</h2></div>
      <ul>
        <li>多条 CLOSE-WAIT：大概率是应用未及时回收套接字。</li>
        <li>高 RTT + 小 cwnd：吞吐受限；短流/间歇发送会让 send/delivery_rate 看起来很低。</li>
        <li>snd_wnd / rcv_space 较大：瓶颈多在 cwnd/RTT 或应用发送模式。</li>
        <li>lastsnd/lastrcv 在 CLOSE-WAIT 上很大：说明半关闭连接长期闲置。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>六、常见问题与建议</h2></div>
      <h3>CLOSE-WAIT 堆积</h3>
      <ul>
        <li>现象：许多连接处于 CLOSE-WAIT 且长时间不变。</li>
        <li>含义：对端 FIN 已到，本端 ACK，但应用未 close()。</li>
        <li>处置：检查 EOF/错误路径的关闭；设置空闲超时与回收；长连接加心跳与读超时。</li>
      </ul>
      <h3>吞吐受限（高 RTT + 小 cwnd）</h3>
      <ul>
        <li>建议：确认确有大流量需求；检查是否小批量/间歇发送；必要时增大发送批量或并发连接。</li>
      </ul>
      <h3>乱序容忍度（reordering 高）</h3>
      <ul>
        <li>解释：高时延/跨洋链路常见，避免乱序误判为丢包；结合重传与路由优化评估。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>七、快速排查清单</h2></div>
      <h3>识别进程与句柄</h3>
      <pre><code>ss -tanp | grep 12809
lsof -i :12809
lsof -p &lt;PID&gt;</code></pre>
      <h3>观察双向套接字</h3>
      <pre><code>ss -tiH 'sport = :12809 or dport = :12809'</code></pre>
      <h3>查看重传/乱序指标</h3>
      <pre><code>netstat -s | egrep -i 'retran|reorder|timeout'</code></pre>
      <h3>应用层检查</h3>
      <ul>
        <li>收到 EOF（read=0）或错误后是否总能 close()</li>
        <li>是否有空闲连接超时与连接池回收策略</li>
        <li>长连接是否有心跳与读/写超时</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>八、速查表（字段与释义）</h2></div>
      <ul>
        <li>State、Recv-Q/Send-Q、wscale、rto、rtt、ato、mss/pmtu、rcvmss/advmss、cwnd/ssthresh、bytes_*、segs_*/data_segs_*、send/delivery_rate、last*、pacing_rate、reordering、rcv_space/rcv_ssthresh、minrtt、snd_wnd、MPTCP、BBR。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>九、参考</h2></div>
      <ul>
        <li>ss(8) 手册：iproute2 工具集</li>
        <li>IETF BBR 论文与 Linux BBR 文档</li>
        <li>MPTCP 文档与 RFC（DSS 映射）</li>
      </ul>
    </section>

    <div class="theme-switcher" title="主题切换">
      <button data-theme="theme-dark">深色</button>
      <button data-theme="theme-blue">淡蓝</button>
      <button data-theme="theme-teal">柔绿</button>
      <button data-theme="theme-warmgray">暖灰</button>
    </div>
  </main>
</body>
</html>

