<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>MPTCP + FEC + TUN 优化与排障指南（精美版）</title>
  <style>
    :root{
      /* 默认：深色（中性暗色，蓝青点缀） */
      --bg:#0b121d; --panel:#0e1626; --card:#101b2b;
      --muted:#93a4b6; --fg:#e5edf6;
      --accent:#60a5fa; --accent-2:#22d3ee;
      --good:#34d399; --warn:#f59e0b; --danger:#f43f5e;
      --border:#1e293b; --table-row:rgba(148,163,184,.07);
      --table-hover:rgba(96,165,250,.10);
      --glow-1: rgba(122,167,255,.22); --glow-2: rgba(92,200,255,.18);
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{
      margin:0; font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
      background: radial-gradient(1100px 520px at 12% -8%, var(--glow-1), transparent),
                  radial-gradient(900px 460px at 108% 8%, var(--glow-2), transparent),
                  var(--bg);
      color: var(--fg);
      line-height: 1.6;
    }

    /* 左对齐布局，与 PVE 页面保持一致 */
    .container{max-width:1120px;margin-left:24px;margin-right:auto;padding:28px 16px 64px}
    header{background:var(--panel);border-bottom:1px solid var(--border)}
    .title{max-width:1120px;margin-left:24px;margin-right:auto;padding:28px 16px}
    h1{margin:0;font-size:28px;letter-spacing:.2px}
    .subtitle{margin-top:6px;color:var(--muted);font-size:14px}
    .badges{display:flex;gap:8px;flex-wrap:wrap;margin-top:14px}
    .badge{padding:4px 10px;border:1px solid var(--border);border-radius:999px;color:var(--accent-2);background:rgba(255,255,255,.04)}

    .grid{display:grid;gap:18px}
    @media (min-width: 980px){.grid.two{grid-template-columns:1fr 1fr}}

    .card{background:linear-gradient(180deg, rgba(255,255,255,.04), rgba(0,0,0,.18));border:1px solid var(--border);border-radius:14px;padding:16px 16px 10px}
    .h2{display:flex;align-items:center;gap:10px}
    .h2 .bar{width:8px;height:18px;background:linear-gradient(180deg, var(--accent), var(--accent-2));border-radius:2px;box-shadow:0 0 0 1px rgba(122,167,255,.25) inset}
    h2{margin:10px 0 4px;font-size:20px}
    h3{margin:10px 0 4px;font-size:16px;color:var(--fg)}
    p{margin:8px 0;color:var(--fg)}

    ul{padding-left:18px;margin:6px 0}
    li{margin:4px 0}

    code{background:rgba(2,6,23,.5);padding:2px 6px;border-radius:6px;border:1px solid var(--border)}
    pre{background:#0c1a2a;border:1px solid var(--border);padding:10px;border-radius:10px;overflow:auto}
    pre code{background:transparent;border:none;padding:0}

    .callout{border:1px solid var(--border);border-left:3px solid var(--accent-2);background:rgba(255,255,255,.05);border-radius:10px;padding:10px 12px;color:var(--fg)}
    .chips{display:flex;gap:8px;flex-wrap:wrap;margin-top:6px}
    .chip{font-size:12px;padding:2px 8px;border-radius:999px;border:1px solid var(--border)}
    .chip.good{color:#bbf7d0;background:rgba(52,211,153,.15)}
    .chip.warn{color:#fde68a;background:rgba(245,158,11,.14)}
    .chip.danger{color:#fecdd3;background:rgba(244,63,94,.16)}

    .kpi{display:flex;gap:10px;flex-wrap:wrap;margin:8px 0}
    .kpi .box{border:1px solid var(--border);background:rgba(12,26,42,.45);border-radius:10px;padding:8px 10px}
    .kpi .n{font-variant-numeric:tabular-nums;color:var(--accent-2)}

    footer{margin-top:24px;color:var(--muted);font-size:12px}
  </style>
  <style>
    /* 主题集：深色（默认）/ 淡蓝 / 柔绿 / 暖灰护眼 */
    :root.theme-dark{--bg:#0b121d;--panel:#0e1626;--card:#101b2b;--muted:#93a4b6;--fg:#e5edf6;--accent:#60a5fa;--accent-2:#22d3ee;--good:#34d399;--warn:#f59e0b;--danger:#f43f5e;--border:#1e293b;--table-row:rgba(148,163,184,.07);--table-hover:rgba(96,165,250,.10)}
    :root.theme-blue{--bg:#0a1624;--panel:#0e1b2b;--card:#0f1f33;--muted:#a9bdd6;--fg:#e8f1fb;--accent:#5cc8ff;--accent-2:#7aa7ff;--good:#5fd5b5;--warn:#f6b15c;--danger:#ff6b88;--border:#16324b;--table-row:rgba(173,202,237,.06);--table-hover:rgba(92,200,255,.10)}
    :root.theme-teal{--bg:#0b1411;--panel:#0e1b16;--card:#102019;--muted:#b6c8be;--fg:#eaf3ee;--accent:#58d3a4;--accent-2:#74c9b6;--good:#6bd3b0;--warn:#e9b562;--danger:#ff7f8a;--border:#193427;--table-row:rgba(150,210,180,.06);--table-hover:rgba(88,211,164,.10)}
    :root.theme-warmgray{--bg:#111315;--panel:#15181b;--card:#1a1f25;--muted:#c2c7cc;--fg:#f0f2f4;--accent:#86a8ff;--accent-2:#8bd3ff;--good:#67d4a3;--warn:#efc070;--danger:#ff8aa0;--border:#262b31;--table-row:rgba(200,205,210,.06);--table-hover:rgba(134,168,255,.10)}

    .theme-switcher{position:fixed;right:16px;bottom:16px;display:flex;gap:8px;z-index:9999}
    .theme-switcher button{padding:6px 10px;border-radius:999px;border:1px solid var(--border);background:rgba(255,255,255,.04);color:var(--fg);cursor:pointer}
    .theme-switcher button.active{outline:2px solid var(--accent)}
  </style>
  <script>
    (function(){
      const key='mptcp-theme';
      const root=document.documentElement;
      const saved=localStorage.getItem(key);
      const initial=saved||'theme-dark';
      root.classList.add(initial);
      window.addEventListener('DOMContentLoaded',()=>{
        document.querySelectorAll('[data-theme]').forEach(btn=>{
          const t=btn.getAttribute('data-theme');
          if(t===initial) btn.classList.add('active');
          btn.addEventListener('click',()=>{
            root.classList.remove('theme-dark','theme-blue','theme-teal','theme-warmgray');
            root.classList.add(t);
            localStorage.setItem(key,t);
            document.querySelectorAll('[data-theme]').forEach(b=>b.classList.remove('active'));
            btn.classList.add('active');
          });
        });
      });
    })();
  </script>

</head>
<body>
  <header>
    <div class="title">
      <h1>MPTCP + FEC + TUN 优化与排障指南</h1>
      <div class="subtitle">左对齐 · 深色风 · 强化可读性（与 PVE 页面一致风格）</div>
      <div class="badges">

        <div class="badge">更新：2025-08-19</div>
        <div class="badge">主题：低时延 · 稳定吞吐 · 降丢包</div>
        <div class="badge">视图：深色·响应式</div>
      </div>
    </div>
  </header>

  <main class="container">
    <section class="card">
      <div class="h2"><span class="bar"></span><h2>概要</h2></div>
      <ul>
        <li>目标：在 A↔B 的 TUN + FEC(UDP) + MPTCP（可能含 redundant）+ shadowsocks(-m none) 体系下，降低时延、减少丢包、提升稳定吞吐。</li>
        <li>现象：wget 时延由 ~270ms 升至 ~700ms；nstat/ss -ti 显示严重乱序与重排（reordering≈120、OFO/DSACK 显著）、BBR 估计带宽很低、MSS 已被收紧（≈1188）。</li>
        <li>结论：瓶颈不在 sslocal/ssserver 算力，而在 FEC 冗余 + 多子流条带化引发的乱序/重排与队列膨胀（bufferbloat），叠加 TUN 用户态/内核态转发开销。</li>
      </ul>
      <div class="chips">
        <span class="chip good">优先稳队列</span>
        <span class="chip warn">适度 FEC</span>
        <span class="chip danger">避免同路径复制</span>
      </div>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>现网关键信号（来自你的实测）</h2></div>
      <ul>
        <li>ss -ti：reordering≈120、delivery_rate 低、cwnd 小、rtt 抖动大（240–260ms）。</li>
        <li>nstat：
          <ul>
            <li>TcpExtTCPOFOQueue=17909（乱序进入队列多）</li>
            <li>MPTcpExtOFOQueue=23462、OFOQueueTail=9229、DuplicateData=3308（MPTCP 层乱序/重复多）</li>
            <li>TCPDSACK* 多、SpuriousRTOs&gt;0（重复/伪超时）</li>
            <li>MPTcpExtMPTCPRetrans=3113、SubflowStale/Recover 非零（子流质量波动）</li>
          </ul>
        </li>
        <li>ping -M do -s 1472 OK（内层 1500 可达），但实际 MSS≈1188（系统已做 MSS 钳制，避免外层分片）。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>根因分析（简明）</h2></div>
      <ol>
        <li>多子流条带化 + FEC 的复制/纠删在多链路 RTT/抖动不等时，放大乱序与 HoL 阻塞，导致应用层首字节与总耗时显著拉长。</li>
        <li>UDP/FEC 没有拥塞控制，复制/纠删带来的突发使隧道与物理口排队膨胀（bufferbloat），BBR 的 btlbw/RTprop 估计被扰动。</li>
        <li>TUN 模式受限于 offload（GRO/GSO/TSO），用户态/内核态拷贝与 pps 压力升高，进一步放大延迟抖动。</li>
      </ol>
      <div class="callout">要点：减少“乱序源头”，并让“到达尽量接近有序”。</div>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>快速收敛方案（建议按序执行）</h2></div>
      <h3>1) 限制条带化强度，减少乱序源头</h3>
      <ul>
        <li>仅保留 1–2 条 RTT/抖动最接近的子流参与传输，其余设为备份。</li>
      </ul>
      <pre><code>ip mptcp endpoint show
ip mptcp endpoint set id &lt;较差子流ID&gt; flags backup
ip mptcp limits set subflow 2
ip mptcp limits set add_addr_accepted 2
</code></pre>

      <h3>2) 选择稳健调度器（两种取舍）</h3>
      <ul>
        <li>低时延：<code>sysctl -w net.mptcp.scheduler=blest</code>（避开慢路径，短流友好）</li>
        <li>冗余抗丢：<code>sysctl -w net.mptcp.scheduler=redundant</code>（仅在“2 条相近路径”上用，且降低/关闭 FEC 复制）</li>
      </ul>

      <h3>3) 收敛 FEC 策略</h3>
      <ul>
        <li>禁止同一路径 1:1 复制（“20:20 同隧道复制”）。</li>
        <li>改用轻量 parity（10%–20%，例如 10:1/20:2/20:4），小 block、适度交织，尽量保持到达接近有序。</li>
      </ul>

      <h3>4) 提高乱序容忍 + 稳队列</h3>
      <ul>
        <li><code>sysctl -w net.ipv4.tcp_reordering=40</code>（30–45 区间微调）。</li>
        <li>保持 fq + BBR，并在隧道/物理口明确设置 qdisc：</li>
      </ul>
      <pre><code>tc qdisc replace dev tun21 root fq
tc qdisc replace dev tun22 root fq
tc qdisc replace dev tun23 root fq
tc qdisc replace dev tun24 root fq
# 物理口（如 eth0）同理；必要时换 fq_codel/cake 抑制突发
</code></pre>

      <h3>5) 统一 MSS 钳制（保持一致）</h3>
      <ul>
        <li>继续对 TUN 出口钳制 MSS（避免不一致导致的分片/乱序）。</li>
      </ul>
      <pre><code>iptables -t mangle -A FORWARD -o tun12 -p tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
# 对 tun21–tun24 同步配置，或在入口统一做
</code></pre>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>两种目标导向的推荐配置</h2></div>
      <h3>A. 低时延/稳定优先（建议先做）</h3>
      <ul>
        <li>子流：2 条相近路径 active，其余 backup。</li>
        <li>调度：blest（首选）；或 redundant（仅 2 条，且 FEC 低比例）。</li>
        <li>FEC：10%–20% parity，小 block。</li>
      </ul>
      <h3>B. 聚合带宽优先</h3>
      <ul>
        <li>子流：2 条相近路径，避免 3–4 条引发重排爆炸。</li>
        <li>调度：default 或 blest，不用 redundant。</li>
        <li>FEC：10%–20%，尽量“到达有序”。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>配置示例（可直接套用，按需调整）</h2></div>
      <h3>MPTCP 端点与限制</h3>
      <pre><code>ip mptcp limits set subflow 2
ip mptcp limits set add_addr_accepted 2
ip mptcp endpoint show
ip mptcp endpoint set id &lt;ID_慢1&gt; flags backup
ip mptcp endpoint set id &lt;ID_慢2&gt; flags backup
</code></pre>
      <h3>调度器（择一）</h3>
      <pre><code>sysctl -w net.mptcp.scheduler=blest
# 或
sysctl -w net.mptcp.scheduler=redundant
</code></pre>
      <h3>乱序容忍</h3>
      <pre><code>sysctl -w net.ipv4.tcp_reordering=40
</code></pre>
      <h3>队列</h3>
      <pre><code>tc qdisc replace dev tun21 root fq
tc qdisc replace dev tun22 root fq
tc qdisc replace dev tun23 root fq
tc qdisc replace dev tun24 root fq
tc qdisc replace dev eth0 root fq
</code></pre>
      <h3>MSS 钳制</h3>
      <pre><code>iptables -t mangle -A FORWARD -o tun12 -p tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
iptables -t mangle -A FORWARD -o tun21 -p tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
# （其它 TUN 口同理）
</code></pre>
      <h3>反向路径过滤</h3>
      <pre><code>sysctl -w net.ipv4.conf.all.rp_filter=0
sysctl -w net.ipv4.conf.default.rp_filter=0
</code></pre>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>客户端并行化（可选，追求更高吞吐）</h2></div>
      <ul>
        <li>双实例 + 双 TUN（不依赖 multiqueue）：</li>
      </ul>
      <ol>
        <li>创建 tun12、tun13，并各自配置地址、up。</li>
        <li>启两套 sslocal，分别绑定 tun12/tun13，连接同一 B 端 ssserver。</li>
        <li>在 A 端用 ECMP 或策略路由实现“按五元组黏性分担”到 tun12/tun13：</li>
      </ol>
      <pre><code>ip route replace default scope global \
  nexthop dev tun12 weight 1 nexthop dev tun13 weight 1
sysctl -w net.ipv4.fib_multipath_hash_policy=1
</code></pre>
      <ul>

        <li>或用 iptables + CONNMARK 做“随机一半 + 黏性”打标，再按 mark 走不同路由表。</li>
        <li>服务端 B：ssserver 启动时开启 <code>--workers N</code>（SO_REUSEPORT），分摊多核。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>观测与验收（优化后 5–10 分钟复测）</h2></div>
      <ul>
        <li>ss -ti：reordering 下降、delivery_rate 上升、cwnd 增大、rtt 偏差减小。</li>
        <li>nstat -az：
          <ul>

            <li>TcpExtTCPOFOQueue、MPTcpExtOFOQueue/OFOQueueTail、TCPDSACK*、MPTcpExtDuplicateData、MPTcpExtMPTCPRetrans 明显下降。</li>
          </ul>
        </li>
        <li>接口/队列：
          <ul>
            <li><code>tc -s qdisc show dev &lt;tun/物理口&gt;</code>（backlog 降低）</li>
            <li><code>ip -s link show &lt;tun/物理口&gt;</code>（drops/errs 降低）</li>
            <li><code>/proc/net/softnet_stat</code>（软中断丢包降低）</li>
          </ul>
        </li>
        <li>业务：wget 首包/总耗时回落接近基线；iperf3 吞吐更稳定。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>常见坑与注意</h2></div>
      <ul>
        <li>FEC 同路径复制 × MPTCP redundant 会“乘法放大”包量与排队，强烈避免；冗余尽量只在一层实现。</li>
        <li>3–4 条 RTT/抖动差异大的子流条带化几乎必然导致 HoL 阻塞，优先限制为 2 条且挑选最相近路径。</li>
        <li>TUN 模式难以充分利用 offload；并行化可考虑“双实例 + 双 TUN”或改走 SOCKS/redir。</li>
        <li>MTU：即使 ping 1472 可达，也要保持 MSS 钳制一致，避免隐式分片；路径变化时重新验证。</li>
      </ul>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>结论速览</h2></div>
      <ul>
        <li>根因是“乱序/重排 + 队列膨胀”，而非 ss 算力；通过“减子流、优调度、轻 FEC、稳队列、保 MSS、一致钳制”，可使时延回落、丢包降低、吞吐稳定。</li>
        <li>建议从“低时延/稳定优先”策略入手：2 条相近子流 + blest（或 2 条 + redundant 且轻 FEC），先观测指标变化，再按需放开并行与带宽。</li>
      </ul>
    </section>

    <div class="theme-switcher" title="主题切换">
      <button data-theme="theme-dark">深色</button>
      <button data-theme="theme-blue">淡蓝</button>
      <button data-theme="theme-teal">柔绿</button>
      <button data-theme="theme-warmgray">暖灰</button>
    </div>

    <footer>© 2025 MPTCP/FEC/TUN 调优速查。样式与布局与本仓库的 ZFS/RAID 指南保持一致（左对齐、深色、卡片化）。</footer>
  </main>
</body>
</html>

