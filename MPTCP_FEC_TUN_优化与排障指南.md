## 概要

- 目标：在 A↔B 的 TUN + FEC(UDP) + MPTCP（可能含 redundant）+ shadowsocks(-m none) 体系下，降低时延、减少丢包、提升稳定吞吐。
- 现象：wget 时延由 ~270ms 升至 ~700ms；nstat/ss -ti 显示严重乱序与重排（reordering≈120、OFO/DSACK 显著）、BBR 估计带宽很低、MSS 已被收紧（≈1188）。
- 结论：瓶颈不在 sslocal/ssserver 算力，而在 FEC 冗余 + 多子流条带化引发的乱序/重排与队列膨胀（bufferbloat），叠加 TUN 用户态/内核态转发开销。

---

## 现网关键信号（来自你的实测）

- ss -ti：reordering≈120、delivery_rate 低、cwnd 小、rtt 抖动大（240–260ms）。
- nstat：
  - TcpExtTCPOFOQueue=17909（乱序进入队列多）
  - MPTcpExtOFOQueue=23462、OFOQueueTail=9229、DuplicateData=3308（MPTCP 层乱序/重复多）
  - TCPDSACK* 多、SpuriousRTOs>0（重复/伪超时）
  - MPTcpExtMPTCPRetrans=3113、SubflowStale/Recover 非零（子流质量波动）
- ping -M do -s 1472 OK（内层 1500 可达），但实际 MSS≈1188（系统已做 MSS 钳制，避免外层分片）。

---

## 根因分析（简明）

1) 多子流条带化 + FEC 的复制/纠删在多链路 RTT/抖动不等时，放大乱序与 HoL 阻塞，导致应用层首字节与总耗时显著拉长。
2) UDP/FEC 没有拥塞控制，复制/纠删带来的突发使隧道与物理口排队膨胀（bufferbloat），BBR 的 btlbw/RTprop 估计被扰动。
3) TUN 模式受限于 offload（GRO/GSO/TSO），用户态/内核态拷贝与 pps 压力升高，进一步放大延迟抖动。

---

## 快速收敛方案（建议按序执行）

1) 限制条带化强度，减少乱序源头
- 仅保留 1–2 条 RTT/抖动最接近的子流参与传输，其余设为备份。
- 命令：
  - ip mptcp endpoint show
  - ip mptcp endpoint set id <较差子流ID> flags backup
  - ip mptcp limits set subflow 2
  - ip mptcp limits set add_addr_accepted 2

2) 选择稳健调度器（两种取舍）
- 低时延：sysctl -w net.mptcp.scheduler=blest（避开慢路径，短流友好）
- 冗余抗丢：sysctl -w net.mptcp.scheduler=redundant（仅在“2 条相近路径”上用，且降低/关闭 FEC 复制）

3) 收敛 FEC 策略
- 禁止同一路径 1:1 复制（“20:20 同隧道复制”）。
- 改用轻量 parity（10%–20%，例如 10:1/20:2/20:4），小 block、适度交织，尽量保持到达接近有序。

4) 提高乱序容忍 + 稳队列
- sysctl -w net.ipv4.tcp_reordering=40（30–45 区间微调）。
- 保持 fq + BBR，并在隧道/物理口明确设置 qdisc：
  - tc qdisc replace dev tun21 root fq
  - tc qdisc replace dev tun22 root fq
  - tc qdisc replace dev tun23 root fq
  - tc qdisc replace dev tun24 root fq
  - 物理口（如 eth0）同理；必要时换 fq_codel/cake 抑制突发。

5) 统一 MSS 钳制（保持一致）
- 继续对 TUN 出口钳制 MSS（避免不一致导致的分片/乱序）：
  - iptables -t mangle -A FORWARD -o tun12 -p tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
  - 对 tun21–tun24 同步配置，或在入口统一做。

---

## 两种目标导向的推荐配置

### A. 低时延/稳定优先（建议先做）
- 子流：2 条相近路径 active，其余 backup。
- 调度：blest（首选）；或 redundant（仅 2 条，且 FEC 低比例）。
- FEC：10%–20% parity，小 block。

### B. 聚合带宽优先
- 子流：2 条相近路径，避免 3–4 条引发重排爆炸。
- 调度：default 或 blest，不用 redundant。
- FEC：10%–20%，尽量“到达有序”。

---

## 配置示例（可直接套用，按需调整）

- MPTCP 端点与限制：
  - ip mptcp limits set subflow 2
  - ip mptcp limits set add_addr_accepted 2
  - ip mptcp endpoint show
  - ip mptcp endpoint set id <ID_慢1> flags backup
  - ip mptcp endpoint set id <ID_慢2> flags backup

- 调度器（择一）：
  - sysctl -w net.mptcp.scheduler=blest
  - 或：sysctl -w net.mptcp.scheduler=redundant

- 乱序容忍：
  - sysctl -w net.ipv4.tcp_reordering=40

- 队列：
  - tc qdisc replace dev tun21 root fq
  - tc qdisc replace dev tun22 root fq
  - tc qdisc replace dev tun23 root fq
  - tc qdisc replace dev tun24 root fq
  - tc qdisc replace dev eth0 root fq

- MSS 钳制：
  - iptables -t mangle -A FORWARD -o tun12 -p tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
  - iptables -t mangle -A FORWARD -o tun21 -p tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
  - （其它 TUN 口同理）

- 反向路径过滤：
  - sysctl -w net.ipv4.conf.all.rp_filter=0
  - sysctl -w net.ipv4.conf.default.rp_filter=0

---

## 客户端并行化（可选，追求更高吞吐）

- 双实例 + 双 TUN（不依赖 multiqueue）：
  1) 创建 tun12、tun13，并各自配置地址、up。
  2) 启两套 sslocal，分别绑定 tun12/tun13，连接同一 B 端 ssserver。
  3) 在 A 端用 ECMP 或策略路由实现“按五元组黏性分担”到 tun12/tun13：
     - ip route replace default scope global \
       nexthop dev tun12 weight 1 nexthop dev tun13 weight 1
     - sysctl -w net.ipv4.fib_multipath_hash_policy=1
  4) 或用 iptables + CONNMARK 做“随机一半 + 黏性”打标，再按 mark 走不同路由表。
- 服务端 B：ssserver 启动时开启 --workers N（SO_REUSEPORT），分摊多核。

---

## 观测与验收（优化后 5–10 分钟复测）

- ss -ti：reordering 下降、delivery_rate 上升、cwnd 增大、rtt 偏差减小。
- nstat -az：
  - TcpExtTCPOFOQueue、MPTcpExtOFOQueue/OFOQueueTail、TCPDSACK*、MPTcpExtDuplicateData、MPTcpExtMPTCPRetrans 明显下降。
- 接口/队列：
  - tc -s qdisc show dev <tun/物理口>（backlog 降低）
  - ip -s link show <tun/物理口>（drops/errs 降低）
  - /proc/net/softnet_stat（软中断丢包降低）
- 业务：wget 首包/总耗时回落接近基线；iperf3 吞吐更稳定。

---

## 常见坑与注意

- FEC 同路径复制 × MPTCP redundant 会“乘法放大”包量与排队，强烈避免；冗余尽量只在一层实现。
- 3–4 条 RTT/抖动差异大的子流条带化几乎必然导致 HoL 阻塞，优先限制为 2 条且挑选最相近路径。
- TUN 模式难以充分利用 offload；并行化可考虑“双实例 + 双 TUN”或改走 SOCKS/redir。
- MTU：即使 ping 1472 可达，也要保持 MSS 钳制一致，避免隐式分片；路径变化时重新验证。

---

## 结论速览

- 此问题的根因是“乱序/重排 + 队列膨胀”，而非 ss 算力；通过“减子流、优调度、轻 FEC、稳队列、保 MSS、一致钳制”，可使时延回落、丢包降低、吞吐稳定。
- 建议从“低时延/稳定优先”策略入手：2 条相近子流 + blest（或 2 条 + redundant 且轻 FEC），立刻观测指标变化，再按需放开并行与带宽。

