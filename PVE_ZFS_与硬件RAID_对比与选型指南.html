<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>PVE 使用 ZFS 与硬件 RAID 对比与选型指南（精美版）</title>
  <style>
    :root{
      /* 默认：深色（中性暗色） */
      --bg:#0b121d; --panel:#0e1626; --card:#101b2b; --muted:#93a4b6; --fg:#e5edf6;
      --accent:#60a5fa; --accent-2:#22d3ee; --good:#34d399; --warn:#f59e0b; --danger:#f43f5e;
      --border:#1e293b; --table-row:rgba(148,163,184,.07); --table-hover:rgba(96,165,250,.10);
      --glow-1: rgba(99,102,241,.25); --glow-2: rgba(34,211,238,.2);
    }
    *{box-sizing:border-box}
    html,body{height:100%}
    body{
      margin:0; font-family: ui-sans-serif, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, "Noto Sans", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
      background: radial-gradient(1200px 600px at 10% -10%, var(--glow-1), transparent),
                  radial-gradient(1000px 500px at 110% 10%, var(--glow-2), transparent),
                  var(--bg);
      color: var(--fg);
      line-height: 1.6;
    }
    .container{max-width:1080px;margin:0 auto;padding:28px 16px 64px}
    header{
      background: var(--panel);
      border-bottom: 1px solid var(--border);
      box-shadow: inset 0 -1px rgba(255,255,255,.05);
    }
    .title{max-width:1080px;margin:0 auto;padding:28px 16px}
    h1{margin:0;font-size:28px;letter-spacing:.2px}
    .subtitle{margin-top:6px;color:var(--muted);font-size:14px}

    .badges{display:flex;gap:8px;flex-wrap:wrap;margin-top:14px}
    .badge{padding:4px 10px;border:1px solid var(--border);border-radius:999px;color:var(--accent-2);background:rgba(255,255,255,.04)}

    h2{margin:28px 0 12px;font-size:20px}
    .h2{display:flex;align-items:center;gap:10px}
    .h2 .bar{width:8px;height:18px;background:linear-gradient(180deg,var(--accent),var(--accent-2));border-radius:2px}
    p{margin:8px 0;color:#dbeafe}

    .grid{display:grid;gap:18px}
    @media (min-width: 980px){.grid.two{grid-template-columns:1fr 1fr}}

    .card{background:linear-gradient(180deg,rgba(148,163,184,.08), rgba(2,6,23,.2));
          border:1px solid var(--border);border-radius:14px;padding:16px 16px 8px;box-shadow:0 10px 30px rgba(2,6,23,.4)}

    .table-wrap{overflow:auto;border-radius:12px;border:1px solid var(--border);background:rgba(2,6,23,.4)}
    table{width:100%;border-collapse:separate;border-spacing:0;min-width:680px}
    th,td{padding:10px 12px;border-bottom:1px solid var(--border);vertical-align:top}
    thead th{position:sticky;top:0;background:linear-gradient(180deg,rgba(99,102,241,.25), rgba(2,6,23,.2));
             color:#e0f2fe;font-weight:600;backdrop-filter: blur(8px)}
    tbody tr:nth-child(odd){background:var(--table-row)}
    tbody tr:hover{background:var(--table-hover)}
    td.num, th.num{text-align:left;font-variant-numeric: tabular-nums}
    code{background:rgba(2,6,23,.5);padding:2px 6px;border-radius:6px;border:1px solid var(--border)}

    .legend{display:flex;gap:10px;flex-wrap:wrap;margin-top:6px}
    .chip{font-size:12px;padding:2px 8px;border-radius:999px;border:1px solid var(--border)}
    .chip.good{color:#bbf7d0;background:rgba(52,211,153,.15)}
    .chip.warn{color:#fde68a;background:rgba(245,158,11,.14)}
    .chip.danger{color:#fecdd3;background:rgba(244,63,94,.16)}

    .tips{color:#cbd5e1;font-size:13px;margin:6px 0 2px}
    .footer{margin-top:24px;color:var(--muted);font-size:12px}

    /* ===== 简洁整齐版覆盖样式（更稳定的几何与对齐） ===== */
    /* 布局与配色更克制，保证表格对齐、减少视觉噪声 */
    .container{max-width:1120px;margin-left:24px;margin-right:auto;padding:28px 16px 64px}
    body{background: var(--bg)}
    header{background:#0f172a}
    .title{max-width:1120px;margin-left:24px;margin-right:auto;padding:28px 16px}
    .card{background:#0b1325;box-shadow:none}
    .table-wrap{background:#0b1325}

    /* 表格：固定布局，统一字号/行高，保证列宽稳定 */
    table{table-layout:fixed}
    th,td{font-size:14px;line-height:1.45}
    thead th{background:#111827;text-align:left}

    /* 表 1：级别/最少盘数/容错 —— 前三列不换行，数值列右对齐，容错列允许换行 */
    .tbl-spec th:nth-child(1),
    .tbl-spec th:nth-child(2),
    .tbl-spec th:nth-child(3),
    .tbl-spec td:nth-child(1),
    .tbl-spec td:nth-child(2),
    .tbl-spec td:nth-child(3){white-space:nowrap}
    .tbl-spec td:nth-child(4),
    .tbl-spec th:nth-child(4){text-align:left}
    .tbl-spec td:nth-child(5){white-space:normal}

    /* 表 2：容量/性能/用途 —— 用途列可换行，其余列尽量单行展示 */
    .tbl-capacity th:nth-child(1),
    .tbl-capacity th:nth-child(2),
    .tbl-capacity th:nth-child(3),
    .tbl-capacity td:nth-child(1),
    .tbl-capacity td:nth-child(2),
    .tbl-capacity td:nth-child(3){white-space:nowrap}
    .tbl-capacity td:nth-child(2),
    .tbl-capacity th:nth-child(2),
    .tbl-capacity td:nth-child(3),
    .tbl-capacity th:nth-child(3){text-align:left}
    .tbl-capacity td:nth-child(4){white-space:normal;word-break:break-word}

    /* 表 3：ZFS vs 硬件 RAID —— 三列均允许正常换行，首列稍窄 */
    .tbl-compare col:nth-child(1){width:18%}
    .tbl-compare col:nth-child(2){width:41%}
    .tbl-compare col:nth-child(3){width:41%}
  </style>
</head>
<body>
  <header>
    <div class="title">
      <h1>PVE 使用 ZFS 与硬件 RAID 对比与选型指南</h1>
      <div class="subtitle">面向浏览器的精美版本 · 端到端校验 · 选型一图掌握</div>
      <div class="badges">
        <div class="badge">更新：2025-08-19</div>
  <style>
    /* 主题集：深色（默认）/ 淡蓝 / 柔绿 / 暖灰护眼 */
    :root.theme-dark{--bg:#0b121d;--panel:#0e1626;--card:#101b2b;--muted:#93a4b6;--fg:#e5edf6;--accent:#60a5fa;--accent-2:#22d3ee;--good:#34d399;--warn:#f59e0b;--danger:#f43f5e;--border:#1e293b;--table-row:rgba(148,163,184,.07);--table-hover:rgba(96,165,250,.10);--glow-1:rgba(99,102,241,.25);--glow-2:rgba(34,211,238,.2)}
    :root.theme-blue{--bg:#0a1624;--panel:#0e1b2b;--card:#0f1f33;--muted:#a9bdd6;--fg:#e8f1fb;--accent:#5cc8ff;--accent-2:#7aa7ff;--good:#5fd5b5;--warn:#f6b15c;--danger:#ff6b88;--border:#16324b;--table-row:rgba(173,202,237,.06);--table-hover:rgba(92,200,255,.10);--glow-1:rgba(122,167,255,.22);--glow-2:rgba(92,200,255,.18)}
    :root.theme-teal{--bg:#0b1411;--panel:#0e1b16;--card:#102019;--muted:#b6c8be;--fg:#eaf3ee;--accent:#58d3a4;--accent-2:#74c9b6;--good:#6bd3b0;--warn:#e9b562;--danger:#ff7f8a;--border:#193427;--table-row:rgba(150,210,180,.06);--table-hover:rgba(88,211,164,.10);--glow-1:rgba(116,200,164,.18);--glow-2:rgba(88,211,164,.14)}
    :root.theme-warmgray{--bg:#111315;--panel:#15181b;--card:#1a1f25;--muted:#c2c7cc;--fg:#f0f2f4;--accent:#86a8ff;--accent-2:#8bd3ff;--good:#67d4a3;--warn:#efc070;--danger:#ff8aa0;--border:#262b31;--table-row:rgba(200,205,210,.06);--table-hover:rgba(134,168,255,.10);--glow-1:rgba(134,168,255,.16);--glow-2:rgba(139,211,255,.14)}

    .theme-switcher{position:fixed;right:16px;bottom:16px;display:flex;gap:8px;z-index:9999}
    .theme-switcher button{padding:6px 10px;border-radius:999px;border:1px solid var(--border);background:rgba(255,255,255,.04);color:var(--fg);cursor:pointer}
    .theme-switcher button.active{outline:2px solid var(--accent)}
  </style>
  <script>
    (function(){
      const key='pve-theme';
      const root=document.documentElement;
      const saved=localStorage.getItem(key);
      const initial=saved||'theme-dark';
      root.classList.add(initial);
      window.addEventListener('DOMContentLoaded',()=>{
        document.querySelectorAll('[data-theme]').forEach(btn=>{
          const t=btn.getAttribute('data-theme');
          if(t===initial) btn.classList.add('active');
          btn.addEventListener('click',()=>{
            root.classList.remove('theme-dark','theme-blue','theme-teal','theme-warmgray');
            root.classList.add(t);
            localStorage.setItem(key,t);
            document.querySelectorAll('[data-theme]').forEach(b=>b.classList.remove('active'));
            btn.classList.add('active');
          });
        });
      });
    })();
  </script>

        <div class="badge">适用：PVE 7/8 + ZFS</div>
        <div class="badge">视图：深色·响应式</div>
      </div>
    </div>
  </header>

  <main class="container">
    <section class="card">
      <div class="h2"><span class="bar"></span><h2>概览</h2></div>
      <p>在 Proxmox VE（PVE）中，ZFS 的“单磁盘、Mirror、RAID10、RAIDZ/2/3、dRAID/2/3”代表不同 vdev 布局，可与传统硬件 RAID 概念类比，但实现不同：ZFS 是文件系统 + 卷管理器，具备端到端校验与写时复制；硬件 RAID 为控制器聚合磁盘。</p>
      <div class="legend">
        <span class="chip good">镜像/RAID10：VM/数据库优选</span>
        <span class="chip warn">RAIDZ：容量效率高但随机写一般</span>
        <span class="chip danger">单盘/RAID0：无冗余，不建议存重要数据</span>
      </div>
    </section>

    <section class="grid two">
      <div class="card">
        <div class="h2"><span class="bar"></span><h2>级别与最少盘数/容错</h2></div>
        <div class="table-wrap">
          <table class="tbl-spec">
            <colgroup>
              <col style="width:18%"><col style="width:22%"><col style="width:20%"><col style="width:20%"><col style="width:20%">
            </colgroup>
            <thead>
              <tr>
                <th>PVE 选项</th><th>ZFS 术语</th><th>硬件 RAID 近似</th><th class="num">最少盘数</th><th class="num">容错（最坏）</th>
              </tr>
            </thead>
            <tbody>
              <tr><td>单磁盘</td><td>stripe（单盘）</td><td>RAID0</td><td class="num">1</td><td class="num">0</td></tr>
              <tr><td>Mirror</td><td>2-way mirror</td><td>RAID1</td><td class="num">2</td><td>每组 1 块</td></tr>
              <tr><td>RAID10</td><td>多镜像条带</td><td>RAID10</td><td class="num">4</td><td>每组 1 块，跨组可多块</td></tr>
              <tr><td>RAIDZ</td><td>raidz1</td><td>RAID5</td><td class="num">3</td><td class="num">1</td></tr>
              <tr><td>RAIDZ2</td><td>raidz2</td><td>RAID6</td><td class="num">4</td><td class="num">2</td></tr>
              <tr><td>RAIDZ3</td><td>raidz3</td><td>三校验</td><td class="num">5</td><td class="num">3</td></tr>
              <tr><td>dRAID</td><td>dRAID(1/2/3)</td><td>分布式 RAIDZ</td><td class="num">≥6（示例）</td><td>与 dRAID1/2/3 相同</td></tr>
            </tbody>
          </table>
        </div>
        <div class="tips">注：同一 vdev 以最小盘容量为准；不建议在同一 vdev 混容量。</div>
      </div>

      <div class="card">
        <div class="h2"><span class="bar"></span><h2>容量与性能/推荐用途</h2></div>
        <div class="table-wrap">
          <table class="tbl-capacity">
            <colgroup>
              <col style="width:22%"><col style="width:28%"><col style="width:20%"><col style="width:30%">
            </colgroup>
            <thead>
              <tr>
                <th>PVE 选项</th><th>有效容量（同容量盘）</th><th>随机 IOPS</th><th>典型用途</th>
              </tr>
            </thead>
            <tbody>
              <tr><td>单磁盘</td><td class="num">1×size</td><td>高（无保护）</td><td>临时/测试</td></tr>
              <tr><td>Mirror</td><td class="num">≈ 50%</td><td>很好（读可并行）</td><td>VM、数据库</td></tr>
              <tr><td>RAID10</td><td class="num">≈ 50%</td><td>很好</td><td>高并发 VM/DB</td></tr>
              <tr><td>RAIDZ</td><td class="num">(N−1)×size</td><td>一般（读改写惩罚）</td><td>备份/归档、大文件</td></tr>
              <tr><td>RAIDZ2</td><td class="num">(N−2)×size</td><td>一般–偏低</td><td>大容量且重要数据；&gt;8TB 推荐</td></tr>
              <tr><td>RAIDZ3</td><td class="num">(N−3)×size</td><td>偏低</td><td>超大容量、极高容错</td></tr>
              <tr><td>dRAID</td><td class="num">(N−校验数−分布式备盘)×size</td><td>与 RAIDZ 类似</td><td>大盘大阵列，重建更快</td></tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>ZFS 与硬件 RAID 的关键差异</h2></div>
      <div class="table-wrap">
        <table class="tbl-compare">
          <colgroup>
            <col style="width:20%"><col style="width:40%"><col style="width:40%">
          </colgroup>
          <thead>
            <tr><th>维度</th><th>ZFS（HBA/JBOD）</th><th>硬件 RAID 控制器</th></tr>
          </thead>
          <tbody>
            <tr><td>数据完整性</td><td>端到端校验 + CoW，自修复（有冗余时）</td><td>多数无端到端校验，易受静默位腐烂</td></tr>
            <tr><td>写洞问题</td><td>事务写入，无写洞</td><td>RAID5/6 可能有写洞（需 BBU/FBWC）</td></tr>
            <tr><td>重建行为</td><td>按“已用数据”<code>resilver</code>，可并行/顺序</td><td>按“整盘容量”重建，时间长风险高</td></tr>
            <tr><td>缓存/日志</td><td>ARC/L2ARC 读缓存；ZIL/SLOG 同步写</td><td>控制器缓存（需断电保护）</td></tr>
            <tr><td>扩容方式</td><td>新增 vdev；组内在线扩展受版本限制</td><td>在线扩盘/扩阵列，依厂商而异</td></tr>
            <tr><td>可移植性</td><td>盘带元数据可跨机读取（同版本）</td><td>控制器绑定，换卡可能读不出</td></tr>
            <tr><td>成本/资源</td><td>免阵列卡；需 CPU/内存（建议 ECC）</td><td>控制器成本高；CPU 卸载在卡上</td></tr>
            <tr><td>最佳实践</td><td>直通 HBA（IT 模式/JBOD），避免堆叠 RAID</td><td>适合提供单一逻辑盘给 OS</td></tr>
          </tbody>
        </table>
      </div>
    </section>

    <section class="grid two">
      <div class="card">
    <div class="theme-switcher" title="主题切换">
      <button data-theme="theme-dark">深色</button>
      <button data-theme="theme-blue">淡蓝</button>
      <button data-theme="theme-teal">柔绿</button>
      <button data-theme="theme-warmgray">暖灰</button>
    </div>

        <div class="h2"><span class="bar"></span><h2>选型建议（PVE 常见场景）</h2></div>
        <ul>
          <li>VM/容器为主、追求低延迟/高 IOPS：优先 Mirror 或 RAID10；需更高读吞吐可增镜像组数。</li>
          <li>备份/媒体库/归档：RAIDZ2/RAIDZ3。8TB+ 大盘强烈建议 RAIDZ2 起步，降低重建风险。</li>
          <li>超大阵列在意重建时间：考虑 dRAID2/dRAID3。</li>
          <li>控制器：优先 HBA 直通或控制器 JBOD，避免“硬件 RAID 上跑 ZFS”。</li>
          <li>资源：ZFS 喜内存，8–16GB 起步更稳；关键业务建议 ECC。</li>
        </ul>
      </div>
      <div class="card">
        <div class="h2"><span class="bar"></span><h2>容量粗算与示例</h2></div>
        <ul>
          <li>Mirror/RAID10：总容量 ≈ 50%（同容量盘）。例：8×12TB → ≈ 48TB。</li>
          <li>RAIDZ/2/3：有效 ≈ <code>(N−P)×单盘</code>（P=1/2/3）。例：8×12TB 的 RAIDZ2 → 72TB（未计格式化开销）。</li>
          <li>dRAID：有效 ≈ <code>(N−P−S)×单盘</code>（S 为分布式备盘条带数）。</li>
        </ul>
      </div>
    </section>

    <section class="card">
      <div class="h2"><span class="bar"></span><h2>与 PVE 界面选项的对应</h2></div>
      <ul>
        <li>“单磁盘 = RAID0”、“Mirror = RAID1”、“RAID10 = 条带化镜像”、“RAIDZ/2/3 ≈ RAID5/6/三校验”。</li>
        <li>最少盘数：1 / 2 / 4 / 3 / 4 / 5；dRAID/dRAID2/dRAID3 取决于布局参数。</li>
      </ul>
      <div class="footer">如需结合你的实际盘数/容量/负载给出更精确的参数（压缩、ashift、recordsize/volblocksize、SLOG/L2ARC），可在此文基础上继续优化。</div>
    </section>
  </main>
</body>
</html>
