# ss -ti 输出字段速查与解读（端口 12809 示例）

面向读者：运维/后端开发
适用系统：Linux 服务器（基于 ss 显示的 TCP 内核指标）
示例命令：
ss -ti '( dport = :12809 )' | more

---

## 一、概要行字段（第一行）
State：TCP 状态。
- ESTAB：连接已建立，可正常收发。
- CLOSE-WAIT：对端已发送 FIN，本端已确认但尚未 close()。长期存在多条通常是应用未及时关闭套接字。

Recv-Q / Send-Q：
- Recv-Q：内核接收队列里、应用还没读走的字节数。
- Send-Q：内核发送队列里、尚未被对端确认的字节数。

Local Address:Port / Peer Address:Port：本地/对端 IP:端口。

---

## 二、详细 TCP 参数（第二行常见字段）
拥塞算法标识：bbr（或 cubic 等）。

wscale:7,7：窗口扩大因子（本端,对端），将 16 位窗口扩展到更大范围。

rto: 重传超时（毫秒）。超时后触发重传。

rtt: 平均往返时延/抖动（毫秒）。示例：rtt:260.11/25.79。

ato: ACK 超时（delayed ACK 相关，毫秒），用于合并确认降低 ACK 数量。

mss: 连接使用的最大报文段（字节，纯负载，不含 TCP/IP 头）。

pmtu: 路径 MTU（字节）。

rcvmss: 最近接收方向估计的 MSS。

advmss: 我方向对端宣告的 MSS（对端向我发送数据时可用的段大小）。

cwnd: 拥塞窗口（单位：段 ≈ MSS 个数），限制“在途未确认的数据量”。

ssthresh: 慢启动阈值（段）。cwnd < ssthresh 为慢启动，≥ 时进入拥塞避免。

bytes_sent / bytes_acked / bytes_received：累计发送/已被确认/接收的字节数。

segs_out / segs_in：发送/接收的 TCP 段计数（含纯 ACK 与重传）。

data_segs_out / data_segs_in：仅统计承载数据的段。

send Xkbps：内核基于 cwnd 与 rtt 的发送速率估计。

lastsnd / lastrcv / lastack：距上次发送数据/接收任意包/收到 ACK 的毫秒数。

pacing_rate：发送整形速率（BBR 会启用 pacing）。

delivery_rate：“有效交付”速率（Goodput，扣除重传/纯 ACK）。

delivered：统计周期内成功交付的数据段数（供 BBR 估算）。

busy：最近时间窗内实际处于“发送忙碌”的时间（毫秒）。

reordering：允许的乱序阈值（单位：段）。阈值大更不易将乱序误判为丢包。

rcv_space：接收缓冲自动调优的目标/当前窗口大小（字节）。

rcv_ssthresh：接收端缓冲自动调优的阈值。

minrtt：观测到的最小 RTT（毫秒），BBR 用作瓶颈排队延迟参考。

snd_wnd：对端通告的窗口（字节），限制发送上界之一（受对端接收能力）。

---

## 三、BBR 专属区块（bbr:(...)）
- bw：BBR 估算的瓶颈带宽（kbps）。
- mrtt：最小 RTT（毫秒）。
- pacing_gain / cwnd_gain：BBR 周期性调节系数；>1 探测带宽，<1 收敛控制。

---

## 四、MPTCP 相关（tcp-ulp-mptcp ...）
- 表示启用了 TCP ULP 的 MPTCP 钩子。
- token：MPTCP 连接标识。
- seq / sfseq / ssnoff / maplen：MPTCP 数据序号与子流序号的映射信息（DSS）。
- flags：子流状态标记。若 token 和 maplen 异常或为 0，可能未协商成功或未实际使用多路径。

---

## 五、结合样例的快速解读要点
1) 多条 CLOSE-WAIT：对端已关闭，但本端进程未 close 套接字。若持续存在，基本可判为应用层连接生命周期管理不完善（可能句柄泄漏）。

2) RTT ~ 230–260ms：高时延环境，小 cwnd（如 4、10、13 段）会显著限制吞吐。BBR 会通过 pacing 和 gain 周期探测，但如果应用本身发送很少或长时间空闲，send/delivery_rate 会偏低，看起来“很慢”。

3) snd_wnd 与 rcv_space 通常较大（~64KB+）：窗口不是主要瓶颈，更可能受 cwnd、RTT 与应用发送模式所限。

4) lastsnd/lastrcv 在 CLOSE-WAIT 上很大：说明这些半关闭连接长期闲置，进一步印证应用未及时回收连接。

---

## 六、常见问题与建议
CLOSE-WAIT 堆积：
- 现象：许多连接处于 CLOSE-WAIT 且长时间不变。
- 含义：对端发送了 FIN，本端已 ACK，但应用未 close()。
- 处置：排查应用在收到 EOF/错误后是否有关闭路径；增加空闲连接超时与回收；对长连接加心跳与读超时。

吞吐受限（高 RTT + 小 cwnd）：
- 现象：BBR 存在，rtt 高，cwnd 小，delivery_rate 低。
- 建议：确认确有大流量需求；检查应用是否小批量/间歇发送导致难以提升 cwnd；必要时增大发送批量或并发连接。

乱序容忍度（reordering 高）：
- 解释：在高时延/无线/跨洋链路常见，避免乱序被误当丢包。
- 建议：观察重传统计，评估是否需要链路/路由优化。

---

## 七、快速排查清单
1) 识别进程与句柄：
   ss -tanp | grep 12809
   lsof -i :12809 或 lsof -p <PID>

2) 观察双向套接字：
   ss -tiH 'sport = :12809 or dport = :12809'

3) 查看重传/乱序指标：
   netstat -s | egrep -i 'retran|reorder|timeout'

4) 应用层检查：
   - 接收到 EOF（read 返回 0）或错误后是否总能走到 close()。
   - 是否有空闲连接超时与连接池回收策略。
   - 长连接是否有心跳与读/写超时。

---

## 八、速查表（字段与释义）
State：ESTAB 已建立；CLOSE-WAIT 本端待关。
Recv-Q/Send-Q：接收/发送队列字节。
wscale：窗口扩大因子。
rto：重传超时（ms）。
rtt：往返时延/抖动（ms）。
ato：ACK 超时（ms）。
mss/pmtu：最大段/路径 MTU。
rcvmss/advmss：接收侧估计 MSS / 我方宣告 MSS。
cwnd/ssthresh：拥塞窗口/慢启动阈值（段）。
bytes_*：累计字节计数。
segs_*/data_segs_*：段计数（含/不含纯 ACK）。
send/delivery_rate：速率估计与有效传输速率。
lastsnd/lastrcv/lastack：距上次活跃的毫秒数。
pacing_rate：发送整形速率。
reordering：乱序阈值（段）。
rcv_space/rcv_ssthresh：接收缓冲与阈值。
minrtt：最小 RTT（ms）。
snd_wnd：对端通告窗口（字节）。
MPTCP（token/seq/...）：多路径映射信息。
BBR（bw/mrtt/gain）：瓶颈带宽、最小 RTT、调节系数。

---

## 九、参考
- ss(8) 手册：iproute2 工具集
- IETF BBR 论文与 Linux BBR 文档
- MPTCP 文档与 RFC（DSS 映射）

